package com.tem.customer.shared.utils

import org.slf4j.MDC
import spock.lang.Specification
import spock.lang.Timeout

import java.util.concurrent.Callable
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * ThreadPoolUtil 单元测试
 * 测试线程池工具类的任务提交、TraceId传递、状态监控等功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class ThreadPoolUtilSpec extends Specification {

    def cleanup() {
        // 清理MDC
        MDC.clear()
    }

    def "测试获取线程池执行器"() {
        when: "获取线程池执行器"
        def executor = ThreadPoolUtil.getExecutor()

        then: "应该返回非空的执行器"
        executor != null
    }

    def "测试提交Runnable任务"() {
        given: "准备一个简单的Runnable任务"
        def executed = false
        def task = new Runnable() {
            @Override
            void run() {
                executed = true
            }
        }

        when: "提交任务"
        def future = ThreadPoolUtil.submit(task)
        future.get(5, TimeUnit.SECONDS)

        then: "任务应该被执行"
        executed == true
        future.isDone()
    }

    def "测试提交Callable任务"() {
        given: "准备一个Callable任务"
        def task = new Callable<String>() {
            @Override
            String call() throws Exception {
                return "任务执行成功"
            }
        }

        when: "提交任务"
        def future = ThreadPoolUtil.submit(task)
        def result = future.get(5, TimeUnit.SECONDS)

        then: "应该返回正确的结果"
        result == "任务执行成功"
        future.isDone()
    }

    @Timeout(10)
    def "测试TraceId传递 - Runnable任务"() {
        given: "设置当前线程的TraceId"
        def originalTraceId = "test-trace-id-123"
        MDC.put("traceId", originalTraceId)
        
        def capturedTraceId = null
        def latch = new CountDownLatch(1)
        
        def task = new Runnable() {
            @Override
            void run() {
                capturedTraceId = MDC.get("traceId")
                latch.countDown()
            }
        }

        when: "提交任务"
        ThreadPoolUtil.submit(task)
        latch.await(5, TimeUnit.SECONDS)

        then: "异步任务中应该能获取到TraceId"
        capturedTraceId == originalTraceId
    }

    @Timeout(10)
    def "测试TraceId传递 - Callable任务"() {
        given: "设置当前线程的TraceId"
        def originalTraceId = "test-trace-id-456"
        MDC.put("traceId", originalTraceId)
        
        def task = new Callable<String>() {
            @Override
            String call() throws Exception {
                return MDC.get("traceId")
            }
        }

        when: "提交任务"
        def future = ThreadPoolUtil.submit(task)
        def capturedTraceId = future.get(5, TimeUnit.SECONDS)

        then: "异步任务中应该能获取到TraceId"
        capturedTraceId == originalTraceId
    }

    def "测试无TraceId时的任务执行"() {
        given: "清除当前线程的TraceId"
        MDC.clear()
        
        def executed = false
        def task = new Runnable() {
            @Override
            void run() {
                executed = true
            }
        }

        when: "提交任务"
        def future = ThreadPoolUtil.submit(task)
        future.get(5, TimeUnit.SECONDS)

        then: "任务应该正常执行"
        executed == true
    }

    def "测试获取线程池状态"() {
        when: "获取线程池状态"
        def status = ThreadPoolUtil.getThreadPoolStatus()

        then: "应该返回有效的状态信息"
        status != null
        status.poolName == "DEFAULT"
        status.corePoolSize >= 0
        status.maximumPoolSize >= 0
        status.activeCount >= 0
        status.poolSize >= 0
        status.queueSize >= 0
        status.completedTaskCount >= 0
        status.taskCount >= 0
        status.isShutdown != null
        status.isTerminated != null
    }

    def "测试并发任务执行"() {
        given: "准备多个并发任务"
        def taskCount = 10
        def latch = new CountDownLatch(taskCount)
        def results = Collections.synchronizedList(new ArrayList<Integer>())
        
        def tasks = (1..taskCount).collect { i ->
            new Runnable() {
                @Override
                void run() {
                    results.add(i)
                    latch.countDown()
                }
            }
        }

        when: "提交所有任务"
        tasks.each { task ->
            ThreadPoolUtil.submit(task)
        }
        latch.await(10, TimeUnit.SECONDS)

        then: "所有任务都应该被执行"
        results.size() == taskCount
        results.containsAll(1..taskCount)
    }

    def "测试任务异常处理"() {
        given: "准备一个会抛出异常的任务"
        def task = new Callable<String>() {
            @Override
            String call() throws Exception {
                throw new RuntimeException("任务执行异常")
            }
        }

        when: "提交任务并获取结果"
        def future = ThreadPoolUtil.submit(task)
        future.get(5, TimeUnit.SECONDS)

        then: "应该抛出异常"
        def ex = thrown(Exception)
        ex.cause.message == "任务执行异常"
    }

    @Timeout(10)
    def "测试MDC上下文完整传递"() {
        given: "设置多个MDC属性"
        MDC.put("traceId", "trace-123")
        MDC.put("userId", "user-456")
        MDC.put("requestId", "req-789")
        
        def capturedMDC = [:]
        def latch = new CountDownLatch(1)
        
        def task = new Runnable() {
            @Override
            void run() {
                capturedMDC.putAll(MDC.getCopyOfContextMap() ?: [:])
                latch.countDown()
            }
        }

        when: "提交任务"
        ThreadPoolUtil.submit(task)
        latch.await(5, TimeUnit.SECONDS)

        then: "所有MDC属性都应该被传递"
        capturedMDC["traceId"] == "trace-123"
        capturedMDC["userId"] == "user-456"
        capturedMDC["requestId"] == "req-789"
    }

    def "测试线程池状态监控"() {
        given: "提交一些任务来改变线程池状态"
        def taskCount = 5
        def latch = new CountDownLatch(taskCount)
        
        (1..taskCount).each { i ->
            ThreadPoolUtil.submit(new Runnable() {
                @Override
                void run() {
                    Thread.sleep(100) // 短暂延迟
                    latch.countDown()
                }
            })
        }

        when: "获取线程池状态"
        def status = ThreadPoolUtil.getThreadPoolStatus()
        latch.await(10, TimeUnit.SECONDS)
        def finalStatus = ThreadPoolUtil.getThreadPoolStatus()

        then: "状态信息应该反映线程池的变化"
        status != null
        finalStatus != null
        finalStatus.completedTaskCount >= status.completedTaskCount
    }

    def "测试自定义线程工厂"() {
        given: "准备一个任务来检查线程名称"
        def threadName = null
        def latch = new CountDownLatch(1)
        
        def task = new Runnable() {
            @Override
            void run() {
                threadName = Thread.currentThread().getName()
                latch.countDown()
            }
        }

        when: "提交任务"
        ThreadPoolUtil.submit(task)
        latch.await(5, TimeUnit.SECONDS)

        then: "线程名称应该包含自定义前缀"
        threadName != null
        threadName.contains("customer-admin-web")
    }

    def "测试线程池资源清理"() {
        given: "设置MDC并提交任务"
        MDC.put("testKey", "testValue")
        
        def taskMDCBefore = null
        def taskMDCAfter = null
        def latch = new CountDownLatch(1)
        
        def task = new Runnable() {
            @Override
            void run() {
                taskMDCBefore = MDC.get("testKey")
                // 模拟任务执行后的清理
                MDC.clear()
                taskMDCAfter = MDC.get("testKey")
                latch.countDown()
            }
        }

        when: "提交任务"
        ThreadPoolUtil.submit(task)
        latch.await(5, TimeUnit.SECONDS)

        then: "MDC应该被正确传递和清理"
        taskMDCBefore == "testValue"
        taskMDCAfter == null
    }
}
