package com.tem.customer.shared.utils

import com.fasterxml.jackson.databind.JsonNode
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDateTime

/**
 * JacksonUtils 单元测试
 * 测试JSON工具类的序列化、反序列化、异常处理等功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class JacksonUtilsSpec extends Specification {

    def "测试私有构造函数"() {
        when: "尝试实例化工具类"
        def utils = new JacksonUtils()

        then: "应该能正常实例化"
        utils != null
    }

    def "测试对象转JSON字符串"() {
        given: "准备测试对象"
        def testObj = [
            name: "张三",
            age: 25,
            email: "<EMAIL>",
            active: true
        ]

        when: "转换为JSON字符串"
        def result = JacksonUtils.toJson(testObj)

        then: "应该返回正确的JSON字符串"
        result != null
        result.contains('"name":"张三"')
        result.contains('"age":25')
        result.contains('"email":"<EMAIL>"')
        result.contains('"active":true')
    }

    def "测试null对象转JSON"() {
        when: "转换null对象"
        def result = JacksonUtils.toJson(null)

        then: "应该返回null字符串"
        result == "null"
    }

    def "测试空对象转JSON"() {
        when: "转换空Map"
        def result = JacksonUtils.toJson([:])

        then: "应该返回空JSON对象"
        result == "{}"
    }

    def "测试对象转JSON字节数组"() {
        given: "准备测试对象"
        def testObj = [name: "测试", value: 123]

        when: "转换为JSON字节数组"
        def result = JacksonUtils.toJsonBytes(testObj)

        then: "应该返回正确的字节数组"
        result != null
        result.length > 0
        new String(result, "UTF-8").contains('"name":"测试"')
    }

    def "测试JSON字符串转对象"() {
        given: "准备JSON字符串"
        def jsonStr = '{"name":"李四","age":30,"active":false}'

        when: "转换为Map对象"
        def result = JacksonUtils.toObj(jsonStr, Map.class)

        then: "应该返回正确的对象"
        result != null
        result.name == "李四"
        result.age == 30
        result.active == false
    }

    def "测试JSON字符串转指定类型对象"() {
        given: "准备JSON字符串和目标类型"
        def jsonStr = '{"name":"王五","age":35}'
        
        when: "转换为指定类型"
        def result = JacksonUtils.toObj(jsonStr, TestUser.class)

        then: "应该返回正确类型的对象"
        result != null
        result instanceof TestUser
        result.name == "王五"
        result.age == 35
    }

    def "测试JSON字节数组转对象"() {
        given: "准备JSON字节数组"
        def jsonBytes = '{"name":"赵六","value":100}'.getBytes("UTF-8")

        when: "转换为Map对象"
        def result = JacksonUtils.toObj(jsonBytes, Map.class)

        then: "应该返回正确的对象"
        result != null
        result.name == "赵六"
        result.value == 100
    }

    def "测试JSON字符串转JsonNode"() {
        given: "准备JSON字符串"
        def jsonStr = '{"name":"测试","items":[1,2,3],"nested":{"key":"value"}}'

        when: "转换为JsonNode"
        def result = JacksonUtils.toObj(jsonStr)

        then: "应该返回JsonNode对象"
        result != null
        result instanceof JsonNode
        result.get("name").asText() == "测试"
        result.get("items").isArray()
        result.get("items").size() == 3
        result.get("nested").get("key").asText() == "value"
    }

    @Unroll
    def "测试JSON格式验证 - 输入: #input, 期望: #expected"() {
        when:
        def result = JacksonUtils.isJson(input)

        then:
        result == expected

        where:
        input                           | expected
        '{"name":"test"}'               | true
        '{"name":"test","age":25}'      | true
        '[1,2,3]'                       | true
        '[]'                            | true
        '{}'                            | true
        'null'                          | true
        'true'                          | true
        'false'                         | true
        '"string"'                      | true
        '123'                           | true
        '123.45'                        | true
        'invalid json'                  | false
        '{"name":}'                     | false
        '{"name":"test",}'              | false
        '{name:"test"}'                 | false
        ''                              | false
    }

    def "测试JSON格式验证 - null值处理"() {
        when: "验证null值"
        JacksonUtils.isJson(null)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "测试序列化异常处理"() {
        given: "准备一个无法序列化的对象"
        def problematicObj = new Object() {
            def getValue() {
                throw new RuntimeException("序列化错误")
            }
        }

        when: "尝试序列化"
        JacksonUtils.toJson(problematicObj)

        then: "应该抛出RuntimeException"
        def ex = thrown(RuntimeException)
        ex.message.contains("JSON serialization failed")
    }

    def "测试反序列化异常处理"() {
        given: "准备无效的JSON字符串"
        def invalidJson = '{"name":"test"'

        when: "尝试反序列化"
        JacksonUtils.toObj(invalidJson, Map.class)

        then: "应该抛出RuntimeException"
        def ex = thrown(RuntimeException)
        ex.message.contains("JSON deserialization failed")
    }

    def "测试时间格式处理"() {
        given: "准备包含时间的对象"
        def testObj = [
            name: "时间测试",
            createTime: LocalDateTime.of(2025, 8, 4, 10, 30, 45)
        ]

        when: "序列化包含时间的对象"
        def jsonStr = JacksonUtils.toJson(testObj)

        then: "应该正确格式化时间"
        jsonStr != null
        jsonStr.contains("2025-08-04 10:30:45")
    }

    def "测试null值处理"() {
        given: "准备包含null值的对象"
        def testObj = [
            name: "测试",
            value: null,
            description: "有值"
        ]

        when: "序列化对象"
        def jsonStr = JacksonUtils.toJson(testObj)

        then: "null值应该被排除"
        jsonStr != null
        jsonStr.contains('"name":"测试"')
        jsonStr.contains('"description":"有值"')
        !jsonStr.contains('"value"')
    }

    def "测试未知属性处理"() {
        given: "准备包含未知属性的JSON"
        def jsonStr = '{"name":"测试","age":25,"unknownField":"unknown","anotherUnknown":123}'

        when: "反序列化为TestUser对象"
        def result = JacksonUtils.toObj(jsonStr, TestUser.class)

        then: "应该忽略未知属性，正常反序列化"
        result != null
        result.name == "测试"
        result.age == 25
    }

    def "测试空字符串转null处理"() {
        given: "准备包含空字符串的JSON"
        def jsonStr = '{"name":"","age":25}'

        when: "反序列化为TestUser对象"
        def result = JacksonUtils.toObj(jsonStr, TestUser.class)

        then: "空字符串应该被转换为null"
        result != null
        result.name == null
        result.age == 25
    }

    def "测试复杂嵌套对象"() {
        given: "准备复杂嵌套对象"
        def complexObj = [
            user: [
                name: "张三",
                profile: [
                    age: 30,
                    hobbies: ["读书", "游泳", "编程"]
                ]
            ],
            metadata: [
                version: "1.0",
                timestamp: System.currentTimeMillis()
            ]
        ]

        when: "序列化和反序列化"
        def jsonStr = JacksonUtils.toJson(complexObj)
        def result = JacksonUtils.toObj(jsonStr, Map.class)

        then: "应该正确处理嵌套结构"
        jsonStr != null
        result != null
        result.user.name == "张三"
        result.user.profile.age == 30
        result.user.profile.hobbies.size() == 3
        result.metadata.version == "1.0"
    }

    // 测试用的简单类
    static class TestUser {
        String name
        Integer age
        
        TestUser() {}
        
        TestUser(String name, Integer age) {
            this.name = name
            this.age = age
        }
    }
}
