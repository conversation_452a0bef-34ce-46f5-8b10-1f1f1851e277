package com.tem.customer.shared.utils

import spock.lang.Specification
import spock.lang.Unroll
/**
 * AmountUtil 单元测试
 * 测试金额工具类的分元转换、精度控制、安全计算等功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class AmountUtilSpec extends Specification {

    def "测试私有构造函数"() {
        when: "尝试实例化工具类"
        new AmountUtil()

        then: "应该抛出UnsupportedOperationException"
        thrown(UnsupportedOperationException)
    }

    @Unroll
    def "测试分转元(Long) - 输入: #input, 期望: #expected"() {
        when:
        def result = AmountUtil.fenToYuan(input)

        then:
        result == expected

        where:
        input | expected
        0L    | new BigDecimal("0.00")
        1L    | new BigDecimal("0.01")
        100L  | new BigDecimal("1.00")
        150L  | new BigDecimal("1.50")
        999L  | new BigDecimal("9.99")
        1000L | new BigDecimal("10.00")
        -100L | new BigDecimal("-1.00")
    }

    def "测试分转元(Long) - null值处理"() {
        when: "转换null值"
        def result = AmountUtil.fenToYuan((Long) null)

        then: "应该返回ZERO"
        result == BigDecimal.ZERO
    }

    @Unroll
    def "测试分转元(BigDecimal) - 输入: #input, 期望: #expected"() {
        when:
        def result = AmountUtil.fenToYuan(input)

        then:
        result == expected

        where:
        input                      | expected
        BigDecimal.ZERO            | new BigDecimal("0.00")
        new BigDecimal("1")        | new BigDecimal("0.01")
        new BigDecimal("100")      | new BigDecimal("1.00")
        new BigDecimal("150")      | new BigDecimal("1.50")
        new BigDecimal("999")      | new BigDecimal("9.99")
        new BigDecimal("1000")     | new BigDecimal("10.00")
        new BigDecimal("-100")     | new BigDecimal("-1.00")
        new BigDecimal("123.45")   | new BigDecimal("1.23")
        new BigDecimal("123.56")   | new BigDecimal("1.24")
    }

    def "测试分转元(BigDecimal) - null值处理"() {
        when: "转换null值"
        def result = AmountUtil.fenToYuan((BigDecimal) null)

        then: "应该返回ZERO"
        result == BigDecimal.ZERO
    }

    @Unroll
    def "测试元转分(String) - 输入: #input, 期望: #expected"() {
        when:
        def result = AmountUtil.yuanToFen(input)

        then:
        result == expected

        where:
        input    | expected
        null     | 0L
        ""       | 0L
        "  "     | 0L
        "0"      | 0L
        "0.00"   | 0L
        "1"      | 100L
        "1.00"   | 100L
        "1.50"   | 150L
        "9.99"   | 999L
        "10.00"  | 1000L
        "-1.00"  | -100L
        "1.234"  | 123L
        "1.235"  | 124L
        "1.236"  | 124L
    }

    def "测试元转分(String) - 无效格式异常"() {
        when:
        AmountUtil.yuanToFen(input)

        then:
        def ex = thrown(IllegalArgumentException)
        ex.message.contains("Invalid amount format")

        where:
        input << ["abc", "1.2.3", "1a", "a1", "1..2", "1,000"]
    }

    @Unroll
    def "测试元转分(BigDecimal) - 输入: #input, 期望: #expected"() {
        when:
        def result = AmountUtil.yuanToFen(input)

        then:
        result == expected

        where:
        input                      | expected
        null                       | 0L
        BigDecimal.ZERO            | 0L
        new BigDecimal("1")        | 100L
        new BigDecimal("1.00")     | 100L
        new BigDecimal("1.50")     | 150L
        new BigDecimal("9.99")     | 999L
        new BigDecimal("10.00")    | 1000L
        new BigDecimal("-1.00")    | -100L
        new BigDecimal("1.234")    | 123L
        new BigDecimal("1.235")    | 124L
        new BigDecimal("1.236")    | 124L
    }

    @Unroll
    def "测试元转分(Long) - 输入: #input, 期望: #expected"() {
        when:
        def result = AmountUtil.yuanToFen(input)

        then:
        result == expected

        where:
        input | expected
        null  | 0L
        0L    | 0L
        1L    | 100L
        10L   | 1000L
        -1L   | -100L
        100L  | 10000L
    }

    @Unroll
    def "测试元转分(BigDecimal返回BigDecimal) - 输入: #input, 期望: #expected"() {
        when:
        def result = AmountUtil.yuanToFenDecimal(input)

        then:
        result == expected

        where:
        input                      | expected
        null                       | BigDecimal.ZERO
        BigDecimal.ZERO            | BigDecimal.ZERO
        new BigDecimal("1")        | new BigDecimal("100")
        new BigDecimal("1.50")     | new BigDecimal("150.00")
        new BigDecimal("9.99")     | new BigDecimal("999.00")
        new BigDecimal("-1.00")    | new BigDecimal("-100.00")
    }

    @Unroll
    def "测试金额是否为正数 - 输入: #input, 期望: #expected"() {
        when:
        def result = AmountUtil.isPositive(input)

        then:
        result == expected

        where:
        input                      | expected
        null                       | false
        BigDecimal.ZERO            | false
        new BigDecimal("0.00")     | false
        new BigDecimal("0.01")     | true
        new BigDecimal("1.00")     | true
        new BigDecimal("-0.01")    | false
        new BigDecimal("-1.00")    | false
        new BigDecimal("100.50")   | true
    }

    @Unroll
    def "测试金额是否为零或负数 - 输入: #input, 期望: #expected"() {
        when:
        def result = AmountUtil.isZeroOrNegative(input)

        then:
        result == expected

        where:
        input                      | expected
        null                       | true
        BigDecimal.ZERO            | true
        new BigDecimal("0.00")     | true
        new BigDecimal("0.01")     | false
        new BigDecimal("1.00")     | false
        new BigDecimal("-0.01")    | true
        new BigDecimal("-1.00")    | true
        new BigDecimal("100.50")   | false
    }

    @Unroll
    def "测试安全金额相加 - 输入: #amount1, #amount2, 期望: #expected"() {
        when:
        def result = AmountUtil.add(amount1, amount2)

        then:
        result == expected

        where:
        amount1                    | amount2                    | expected
        null                       | null                       | BigDecimal.ZERO
        null                       | new BigDecimal("1.00")     | new BigDecimal("1.00")
        new BigDecimal("1.00")     | null                       | new BigDecimal("1.00")
        new BigDecimal("1.00")     | new BigDecimal("2.00")     | new BigDecimal("3.00")
        new BigDecimal("1.50")     | new BigDecimal("2.30")     | new BigDecimal("3.80")
        new BigDecimal("-1.00")    | new BigDecimal("2.00")     | new BigDecimal("1.00")
        new BigDecimal("0.00")     | new BigDecimal("0.00")     | BigDecimal.ZERO
    }

    @Unroll
    def "测试安全金额相减 - 输入: #amount1, #amount2, 期望: #expected"() {
        when:
        def result = AmountUtil.subtract(amount1, amount2)

        then:
        result == expected

        where:
        amount1                    | amount2                    | expected
        null                       | null                       | BigDecimal.ZERO
        null                       | new BigDecimal("1.00")     | new BigDecimal("-1.00")
        new BigDecimal("1.00")     | null                       | new BigDecimal("1.00")
        new BigDecimal("3.00")     | new BigDecimal("2.00")     | new BigDecimal("1.00")
        new BigDecimal("3.80")     | new BigDecimal("2.30")     | new BigDecimal("1.50")
        new BigDecimal("1.00")     | new BigDecimal("2.00")     | new BigDecimal("-1.00")
        new BigDecimal("0.00")     | new BigDecimal("0.00")     | BigDecimal.ZERO
    }

    def "测试精度控制 - 四舍五入"() {
        when: "测试各种需要四舍五入的场景"
        def result1 = AmountUtil.fenToYuan(1234L)  // 12.34分 -> 0.12元
        def result2 = AmountUtil.fenToYuan(1235L)  // 12.35分 -> 0.12元 (四舍五入)
        def result3 = AmountUtil.fenToYuan(1236L)  // 12.36分 -> 0.12元
        def result4 = AmountUtil.yuanToFen("1.234") // 1.234元 -> 123分 (四舍五入)
        def result5 = AmountUtil.yuanToFen("1.235") // 1.235元 -> 124分 (四舍五入)

        then: "验证四舍五入结果"
        result1 == new BigDecimal("12.34")
        result2 == new BigDecimal("12.35")
        result3 == new BigDecimal("12.36")
        result4 == 123L
        result5 == 124L
    }

    def "测试边界值处理"() {
        when: "测试极大值和极小值"
        def maxLong = Long.MAX_VALUE
        def minLong = Long.MIN_VALUE
        def result1 = AmountUtil.fenToYuan(maxLong)
        def result2 = AmountUtil.fenToYuan(minLong)

        then: "应该正确处理边界值"
        result1 != null
        result2 != null
        result1.scale() == 2
        result2.scale() == 2
    }
}
