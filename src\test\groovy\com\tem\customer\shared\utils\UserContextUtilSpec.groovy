package com.tem.customer.shared.utils

import org.springframework.mock.web.MockHttpServletRequest
import org.springframework.mock.web.MockHttpServletResponse
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import spock.lang.Specification

import jakarta.servlet.http.HttpSession

/**
 * UserContextUtil 单元测试
 * 测试用户上下文工具类的双模式兼容、参数获取等功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class UserContextUtilSpec extends Specification {

    MockHttpServletRequest mockRequest
    MockHttpServletResponse mockResponse
    HttpSession mockSession

    def setup() {
        mockRequest = new MockHttpServletRequest()
        mockResponse = new MockHttpServletResponse()
        mockSession = mockRequest.getSession(true)
        
        // 设置Spring的RequestContextHolder
        def requestAttributes = new ServletRequestAttributes(mockRequest, mockResponse)
        RequestContextHolder.setRequestAttributes(requestAttributes)
    }

    def cleanup() {
        // 清理RequestContextHolder
        RequestContextHolder.resetRequestAttributes()
    }

    def "测试获取当前HTTP请求"() {
        when: "获取当前HTTP请求"
        def request = UserContextUtil.getCurrentRequest()

        then: "应该返回正确的请求对象"
        request != null
        request == mockRequest
    }

    def "测试获取当前HTTP响应"() {
        when: "获取当前HTTP响应"
        def response = UserContextUtil.getCurrentResponse()

        then: "应该返回正确的响应对象"
        response != null
        response == mockResponse
    }

    def "测试获取当前Session"() {
        when: "获取当前Session"
        def session = UserContextUtil.getCurrentSession()

        then: "应该返回正确的Session对象"
        session != null
        session == mockSession
    }

    def "测试非Web环境下获取请求对象"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "获取当前HTTP请求"
        def request = UserContextUtil.getCurrentRequest()

        then: "应该返回null"
        request == null
    }

    def "测试获取请求参数"() {
        given: "设置请求参数"
        mockRequest.setParameter("userId", "12345")
        mockRequest.setParameter("userName", "张三")
        mockRequest.setParameter("page", "1")

        when: "获取请求参数"
        def userId = UserContextUtil.getParameter("userId")
        def userName = UserContextUtil.getParameter("userName")
        def page = UserContextUtil.getParameter("page")
        def nonExistent = UserContextUtil.getParameter("nonExistent")

        then: "应该返回正确的参数值"
        userId == "12345"
        userName == "张三"
        page == "1"
        nonExistent == null
    }

    def "测试获取请求头"() {
        given: "设置请求头"
        mockRequest.addHeader("Authorization", "Bearer token123")
        mockRequest.addHeader("Content-Type", "application/json")
        mockRequest.addHeader("X-Request-ID", "req-456")

        when: "获取请求头"
        def auth = UserContextUtil.getHeader("Authorization")
        def contentType = UserContextUtil.getHeader("Content-Type")
        def requestId = UserContextUtil.getHeader("X-Request-ID")
        def nonExistent = UserContextUtil.getHeader("NonExistent")

        then: "应该返回正确的请求头值"
        auth == "Bearer token123"
        contentType == "application/json"
        requestId == "req-456"
        nonExistent == null
    }

    def "测试Session属性操作"() {
        given: "准备Session属性"
        def key = "userInfo"
        def value = [id: 1, name: "李四", role: "admin"]

        when: "设置和获取Session属性"
        UserContextUtil.setSessionAttribute(key, value)
        def retrievedValue = UserContextUtil.getSessionAttribute(key)

        then: "应该正确设置和获取Session属性"
        retrievedValue == value
    }

    def "测试Session属性移除"() {
        given: "设置Session属性"
        def key = "tempData"
        def value = "临时数据"
        UserContextUtil.setSessionAttribute(key, value)

        when: "移除Session属性"
        UserContextUtil.removeSessionAttribute(key)
        def retrievedValue = UserContextUtil.getSessionAttribute(key)

        then: "属性应该被移除"
        retrievedValue == null
    }

    def "测试获取客户端IP地址"() {
        given: "设置客户端IP相关的请求头"
        mockRequest.addHeader("X-Forwarded-For", "*************")
        mockRequest.setRemoteAddr("127.0.0.1")

        when: "获取客户端IP地址"
        def clientIp = UserContextUtil.getClientIpAddress()

        then: "应该返回正确的IP地址"
        clientIp == "*************"
    }

    def "测试获取用户代理"() {
        given: "设置User-Agent请求头"
        def userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        mockRequest.addHeader("User-Agent", userAgent)

        when: "获取用户代理"
        def retrievedUserAgent = UserContextUtil.getUserAgent()

        then: "应该返回正确的User-Agent"
        retrievedUserAgent == userAgent
    }

    def "测试获取请求URI"() {
        given: "设置请求URI"
        mockRequest.setRequestURI("/api/users/profile")

        when: "获取请求URI"
        def requestUri = UserContextUtil.getRequestUri()

        then: "应该返回正确的URI"
        requestUri == "/api/users/profile"
    }

    def "测试获取请求方法"() {
        given: "设置请求方法"
        mockRequest.setMethod("POST")

        when: "获取请求方法"
        def method = UserContextUtil.getRequestMethod()

        then: "应该返回正确的请求方法"
        method == "POST"
    }

    def "测试获取完整请求URL"() {
        given: "设置请求URL相关信息"
        mockRequest.setScheme("https")
        mockRequest.setServerName("api.example.com")
        mockRequest.setServerPort(443)
        mockRequest.setRequestURI("/api/users")
        mockRequest.setQueryString("page=1&size=10")

        when: "获取完整请求URL"
        def requestUrl = UserContextUtil.getRequestUrl()

        then: "应该返回完整的URL"
        requestUrl.contains("https://api.example.com")
        requestUrl.contains("/api/users")
        requestUrl.contains("page=1&size=10")
    }

    def "测试获取查询字符串"() {
        given: "设置查询字符串"
        mockRequest.setQueryString("name=张三&age=25&active=true")

        when: "获取查询字符串"
        def queryString = UserContextUtil.getQueryString()

        then: "应该返回正确的查询字符串"
        queryString == "name=张三&age=25&active=true"
    }

    def "测试非Web环境下的参数获取"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "尝试获取参数"
        def param = UserContextUtil.getParameter("test")
        def header = UserContextUtil.getHeader("test")
        def sessionAttr = UserContextUtil.getSessionAttribute("test")

        then: "应该返回null或默认值"
        param == null
        header == null
        sessionAttr == null
    }

    def "测试Session无效时的属性操作"() {
        given: "清除RequestContextHolder"
        RequestContextHolder.resetRequestAttributes()

        when: "尝试操作Session属性"
        UserContextUtil.setSessionAttribute("test", "value")
        def value = UserContextUtil.getSessionAttribute("test")

        then: "应该安全处理，不抛出异常"
        value == null
    }

    def "测试获取多个请求参数"() {
        given: "设置多个同名参数"
        mockRequest.setParameter("tags", "java", "spring", "boot")

        when: "获取参数数组"
        def tags = UserContextUtil.getParameterValues("tags")

        then: "应该返回所有参数值"
        tags != null
        tags.length == 3
        tags.contains("java")
        tags.contains("spring")
        tags.contains("boot")
    }

    def "测试获取所有请求参数"() {
        given: "设置多个请求参数"
        mockRequest.setParameter("name", "王五")
        mockRequest.setParameter("age", "30")
        mockRequest.setParameter("city", "北京")

        when: "获取所有参数"
        def parameterMap = UserContextUtil.getParameterMap()

        then: "应该返回所有参数"
        parameterMap != null
        parameterMap.size() >= 3
        parameterMap["name"] != null
        parameterMap["age"] != null
        parameterMap["city"] != null
    }

    def "测试请求属性操作"() {
        given: "准备请求属性"
        def key = "requestData"
        def value = [timestamp: System.currentTimeMillis(), source: "test"]

        when: "设置和获取请求属性"
        UserContextUtil.setRequestAttribute(key, value)
        def retrievedValue = UserContextUtil.getRequestAttribute(key)

        then: "应该正确设置和获取请求属性"
        retrievedValue == value
    }

    def "测试请求属性移除"() {
        given: "设置请求属性"
        def key = "tempRequestData"
        def value = "临时请求数据"
        UserContextUtil.setRequestAttribute(key, value)

        when: "移除请求属性"
        UserContextUtil.removeRequestAttribute(key)
        def retrievedValue = UserContextUtil.getRequestAttribute(key)

        then: "属性应该被移除"
        retrievedValue == null
    }

    def "测试获取请求内容类型"() {
        given: "设置Content-Type"
        mockRequest.setContentType("application/json; charset=UTF-8")

        when: "获取内容类型"
        def contentType = UserContextUtil.getContentType()

        then: "应该返回正确的内容类型"
        contentType == "application/json; charset=UTF-8"
    }

    def "测试获取请求字符编码"() {
        given: "设置字符编码"
        mockRequest.setCharacterEncoding("UTF-8")

        when: "获取字符编码"
        def encoding = UserContextUtil.getCharacterEncoding()

        then: "应该返回正确的字符编码"
        encoding == "UTF-8"
    }
}
