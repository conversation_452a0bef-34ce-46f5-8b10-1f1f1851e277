package com.tem.customer.shared.utils

import spock.lang.Specification

/**
 * SafeSpelUtil 单元测试
 * 测试安全的SpEL表达式工具类的各种功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class SafeSpelUtilSpec extends Specification {

    def "测试安全表达式解析 - 基本功能"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析安全表达式"
        def parseResult = SafeSpelUtil.safeParseExpression("#result", method, args, result)

        then: "应该返回正确的结果"
        parseResult == "test"
    }

    def "测试Long表达式解析"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = 123L

        when: "解析Long表达式"
        def parseResult = SafeSpelUtil.parseLongExpression("#result", method, args, result)

        then: "应该返回正确的Long结果"
        parseResult == 123L
    }

    def "测试String表达式解析"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "Hello World"

        when: "解析String表达式"
        def parseResult = SafeSpelUtil.parseStringExpression("#result", method, args, result)

        then: "应该返回正确的String结果"
        parseResult == "Hello World"
    }

    def "测试null表达式处理"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析null表达式"
        def parseResult = SafeSpelUtil.safeParseExpression(null, method, args, result)

        then: "应该返回null"
        parseResult == null
    }

    def "测试空表达式处理"() {
        given: "准备测试参数"
        def method = String.class.getMethod("toString")
        def args = [] as Object[]
        def result = "test"

        when: "解析空表达式"
        def parseResult = SafeSpelUtil.safeParseExpression("", method, args, result)

        then: "应该返回null"
        parseResult == null
    }
}
